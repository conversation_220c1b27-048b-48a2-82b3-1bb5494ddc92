package com.ruoyi;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;

/**
 * 最简单的Excel读取测试类
 * 演示如何一步步读取Excel文件并显示在控制台
 */
public class ExcelReadTest {

    public static void main(String[] args) {
        // 第一步：生成测试Excel文件
        System.out.println("=== 第一步：生成测试Excel文件 ===");
        ExcelGenerator.generateTestExcel("test.xlsx");
        
        System.out.println("\n=== 第二步：读取Excel文件 ===");
        // 第二步：读取Excel文件
        simpleReadExcel("test.xlsx");
    }

    /**
     * 最简单的Excel读取方法
     */
    public static void simpleReadExcel(String fileName) {
        try {
            // 1. 打开Excel文件
            FileInputStream file = new FileInputStream(fileName);
            System.out.println("✓ 成功打开文件: " + fileName);

            // 2. 创建工作簿对象
            Workbook workbook = new XSSFWorkbook(file);
            System.out.println("✓ 成功创建工作簿对象");

            // 3. 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            System.out.println("✓ 获取工作表: " + sheet.getSheetName());
            System.out.println("✓ 工作表总行数: " + (sheet.getLastRowNum() + 1));

            System.out.println("\n--- 开始读取数据 ---");

            // 4. 遍历每一行
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                
                if (row == null) {
                    System.out.println("第" + (i + 1) + "行: [空行]");
                    continue;
                }

                System.out.print("第" + (i + 1) + "行: ");

                // 5. 遍历每一列
                for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
                    Cell cell = row.getCell(j);
                    
                    // 6. 获取单元格的值
                    String cellValue = getCellValue(cell);
                    System.out.print(cellValue);
                    
                    // 如果不是最后一列，添加分隔符
                    if (j < row.getPhysicalNumberOfCells() - 1) {
                        System.out.print(" | ");
                    }
                }
                System.out.println(); // 换行
            }

            // 7. 关闭资源
            workbook.close();
            file.close();
            System.out.println("\n✓ 文件读取完成，资源已关闭");

        } catch (IOException e) {
            System.err.println("❌ 读取Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取单元格的值（简化版本）
     */
    private static String getCellValue(Cell cell) {
        if (cell == null) {
            return "[空]";
        }

        // 根据单元格类型获取值
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            
            case NUMERIC:
                // 检查是否是日期
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 是数字
                    double value = cell.getNumericCellValue();
                    // 如果是整数，不显示小数点
                    if (value == (long) value) {
                        return String.valueOf((long) value);
                    } else {
                        return String.valueOf(value);
                    }
                }
            
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            
            case BLANK:
                return "[空白]";
            
            default:
                return cell.toString();
        }
    }

    /**
     * 演示如何读取特定的单元格
     */
    public static void readSpecificCells(String fileName) {
        System.out.println("\n=== 读取特定单元格示例 ===");
        
        try (FileInputStream file = new FileInputStream(fileName);
             Workbook workbook = new XSSFWorkbook(file)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 读取A1单元格（第0行第0列）
            Cell cellA1 = sheet.getRow(0).getCell(0);
            System.out.println("A1单元格的值: " + getCellValue(cellA1));
            
            // 读取B2单元格（第1行第1列）
            Cell cellB2 = sheet.getRow(1).getCell(1);
            System.out.println("B2单元格的值: " + getCellValue(cellB2));
            
            // 读取C3单元格（第2行第2列）
            Cell cellC3 = sheet.getRow(2).getCell(2);
            System.out.println("C3单元格的值: " + getCellValue(cellC3));
            
        } catch (IOException e) {
            System.err.println("读取特定单元格时出错: " + e.getMessage());
        }
    }

    /**
     * 演示如何只读取表头
     */
    public static void readHeaders(String fileName) {
        System.out.println("\n=== 读取表头示例 ===");
        
        try (FileInputStream file = new FileInputStream(fileName);
             Workbook workbook = new XSSFWorkbook(file)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0); // 假设第一行是表头
            
            if (headerRow != null) {
                System.out.print("表头: ");
                for (int i = 0; i < headerRow.getPhysicalNumberOfCells(); i++) {
                    Cell cell = headerRow.getCell(i);
                    System.out.print(getCellValue(cell));
                    if (i < headerRow.getPhysicalNumberOfCells() - 1) {
                        System.out.print(" | ");
                    }
                }
                System.out.println();
            }
            
        } catch (IOException e) {
            System.err.println("读取表头时出错: " + e.getMessage());
        }
    }

    /**
     * 演示如何统计数据
     */
    public static void analyzeData(String fileName) {
        System.out.println("\n=== 数据分析示例 ===");
        
        try (FileInputStream file = new FileInputStream(fileName);
             Workbook workbook = new XSSFWorkbook(file)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            int totalRows = sheet.getLastRowNum() + 1;
            int dataRows = totalRows - 1; // 减去表头行
            int totalColumns = sheet.getRow(0).getPhysicalNumberOfCells();
            
            System.out.println("总行数: " + totalRows);
            System.out.println("数据行数: " + dataRows);
            System.out.println("总列数: " + totalColumns);
            
            // 统计第3列（age列）的平均值
            if (totalColumns >= 3) {
                double sum = 0;
                int count = 0;
                
                for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 从第2行开始（跳过表头）
                    Row row = sheet.getRow(i);
                    if (row != null) {
                        Cell cell = row.getCell(2); // 第3列（索引为2）
                        if (cell != null && cell.getCellType() == CellType.NUMERIC) {
                            sum += cell.getNumericCellValue();
                            count++;
                        }
                    }
                }
                
                if (count > 0) {
                    double average = sum / count;
                    System.out.println("年龄列平均值: " + String.format("%.2f", average));
                }
            }
            
        } catch (IOException e) {
            System.err.println("分析数据时出错: " + e.getMessage());
        }
    }
}
