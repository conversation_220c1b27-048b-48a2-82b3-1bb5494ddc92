package com.ruoyi;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 简单的Excel读取示例 - 直接在控制台显示内容
 */
public class SimpleExcelReader {

    public static void main(String[] args) {
        // 先生成一个测试Excel文件
        ExcelGenerator.generateTestExcel("test.xlsx");
        
        // 然后读取并显示
        readAndDisplayExcel("test.xlsx");
    }

    /**
     * 读取Excel文件并在控制台显示
     * @param filePath Excel文件路径
     */
    public static void readAndDisplayExcel(String filePath) {
        System.out.println("开始读取Excel文件: " + filePath);
        System.out.println("=" * 80);

        try (FileInputStream fis = new FileInputStream(filePath)) {
            // 根据文件扩展名创建对应的Workbook
            Workbook workbook;
            if (filePath.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (filePath.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(fis);
            } else {
                System.out.println("不支持的文件格式！仅支持.xls和.xlsx文件");
                return;
            }

            // 显示工作簿信息
            System.out.println("工作簿包含 " + workbook.getNumberOfSheets() + " 个工作表");
            
            // 遍历所有工作表
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                displaySheet(sheet, sheetIndex);
            }

            workbook.close();
            
        } catch (IOException e) {
            System.err.println("读取Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示工作表内容
     * @param sheet 工作表对象
     * @param sheetIndex 工作表索引
     */
    private static void displaySheet(Sheet sheet, int sheetIndex) {
        System.out.println("\n第" + (sheetIndex + 1) + "个工作表: " + sheet.getSheetName());
        System.out.println("总行数: " + (sheet.getLastRowNum() + 1));
        
        if (sheet.getPhysicalNumberOfRows() == 0) {
            System.out.println("工作表为空");
            return;
        }

        // 获取第一行来确定列数
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            System.out.println("第一行为空");
            return;
        }
        
        int columnCount = firstRow.getPhysicalNumberOfCells();
        System.out.println("总列数: " + columnCount);
        System.out.println("-" * 80);

        // 显示表头（如果第一行是表头）
        System.out.println("表头:");
        displayRow(firstRow, 0);
        System.out.println("-" * 80);

        // 显示数据行
        System.out.println("数据内容:");
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                displayRow(row, rowIndex);
            } else {
                System.out.println("第" + (rowIndex + 1) + "行: [空行]");
            }
        }
        System.out.println("=" * 80);
    }

    /**
     * 显示单行数据
     * @param row 行对象
     * @param rowIndex 行索引
     */
    private static void displayRow(Row row, int rowIndex) {
        StringBuilder rowContent = new StringBuilder();
        rowContent.append("第").append(rowIndex + 1).append("行: ");

        for (int cellIndex = 0; cellIndex < row.getPhysicalNumberOfCells(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            String cellValue = getCellValueAsString(cell);
            
            // 格式化显示，每个单元格占15个字符宽度
            rowContent.append(String.format("%-15s", cellValue));
            
            if (cellIndex < row.getPhysicalNumberOfCells() - 1) {
                rowContent.append(" | ");
            }
        }

        System.out.println(rowContent.toString());
    }

    /**
     * 获取单元格的值并转换为字符串
     * @param cell 单元格对象
     * @return 单元格值的字符串表示
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "[空]";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
                
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 如果是日期格式
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(date);
                } else {
                    // 如果是数字
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，不显示小数点
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
                
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
                
            case FORMULA:
                // 显示公式和计算结果
                try {
                    return cell.getStringCellValue() + " (公式)";
                } catch (Exception e) {
                    return cell.getCellFormula() + " (公式)";
                }
                
            case BLANK:
                return "[空白]";
                
            case ERROR:
                return "[错误:" + cell.getErrorCellValue() + "]";
                
            default:
                return cell.toString();
        }
    }

    /**
     * 显示详细的单元格信息（用于调试）
     * @param cell 单元格对象
     * @param rowIndex 行索引
     * @param cellIndex 列索引
     */
    private static void displayCellDetails(Cell cell, int rowIndex, int cellIndex) {
        System.out.println("单元格[" + (rowIndex + 1) + "," + (cellIndex + 1) + "]:");
        
        if (cell == null) {
            System.out.println("  值: null");
            return;
        }

        System.out.println("  类型: " + cell.getCellType());
        System.out.println("  值: " + getCellValueAsString(cell));
        
        if (cell.getCellType() == CellType.NUMERIC && DateUtil.isCellDateFormatted(cell)) {
            System.out.println("  日期值: " + cell.getDateCellValue());
        }
        
        if (cell.getCellStyle() != null) {
            System.out.println("  格式: " + cell.getCellStyle().getDataFormatString());
        }
        
        System.out.println();
    }

    /**
     * 读取并显示Excel文件的详细信息（包括每个单元格的详细信息）
     * @param filePath Excel文件路径
     */
    public static void readAndDisplayExcelDetails(String filePath) {
        System.out.println("详细读取Excel文件: " + filePath);
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook;
            if (filePath.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else {
                workbook = new HSSFWorkbook(fis);
            }

            Sheet sheet = workbook.getSheetAt(0); // 只读取第一个工作表
            
            System.out.println("工作表名称: " + sheet.getSheetName());
            System.out.println("=" * 50);

            for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) continue;

                System.out.println("第" + (rowIndex + 1) + "行:");
                for (int cellIndex = 0; cellIndex < row.getPhysicalNumberOfCells(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    displayCellDetails(cell, rowIndex, cellIndex);
                }
                System.out.println("-" * 30);
            }

            workbook.close();
            
        } catch (IOException e) {
            System.err.println("读取Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
