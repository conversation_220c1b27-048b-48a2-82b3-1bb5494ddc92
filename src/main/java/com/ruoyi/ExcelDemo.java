package com.ruoyi;

/**
 * Excel读取完整演示程序
 * 这个类展示了从生成Excel文件到读取显示的完整流程
 */
public class ExcelDemo {
    
    public static void main(String[] args) {
        System.out.println("🚀 Excel读取演示程序启动");
        System.out.println("=" * 60);
        
        // 演示1：基本读取
        demo1_BasicReading();
        
        // 演示2：读取特定单元格
        demo2_SpecificCells();
        
        // 演示3：读取表头
        demo3_Headers();
        
        // 演示4：数据分析
        demo4_DataAnalysis();
        
        // 演示5：格式化显示
        demo5_FormattedDisplay();
        
        System.out.println("\n🎉 所有演示完成！");
    }
    
    /**
     * 演示1：基本的Excel读取
     */
    private static void demo1_BasicReading() {
        System.out.println("\n📖 演示1：基本Excel读取");
        System.out.println("-" * 40);
        
        // 先生成测试文件
        ExcelGenerator.generateTestExcel("demo.xlsx");
        
        // 然后读取
        ExcelReadTest.simpleReadExcel("demo.xlsx");
    }
    
    /**
     * 演示2：读取特定单元格
     */
    private static void demo2_SpecificCells() {
        System.out.println("\n🎯 演示2：读取特定单元格");
        System.out.println("-" * 40);
        
        ExcelReadTest.readSpecificCells("demo.xlsx");
    }
    
    /**
     * 演示3：读取表头
     */
    private static void demo3_Headers() {
        System.out.println("\n📋 演示3：读取表头");
        System.out.println("-" * 40);
        
        ExcelReadTest.readHeaders("demo.xlsx");
    }
    
    /**
     * 演示4：数据分析
     */
    private static void demo4_DataAnalysis() {
        System.out.println("\n📊 演示4：数据分析");
        System.out.println("-" * 40);
        
        ExcelReadTest.analyzeData("demo.xlsx");
    }
    
    /**
     * 演示5：格式化显示
     */
    private static void demo5_FormattedDisplay() {
        System.out.println("\n🎨 演示5：格式化显示");
        System.out.println("-" * 40);
        
        SimpleExcelReader.readAndDisplayExcel("demo.xlsx");
    }
}
