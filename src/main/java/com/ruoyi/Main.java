package com.ruoyi;

import java.util.*;

public class Main {
    public static void main(String[] args) {
        System.out.println("Excel数据读取和比对示例");

        // 示例1: 读取Excel文件
        demonstrateExcelReading();

        // 示例2: 数据比对
        demonstrateDataComparison();
    }

    /**
     * 演示Excel文件读取
     */
    private static void demonstrateExcelReading() {
        System.out.println("\n=== Excel文件读取示例 ===");

        // 注意: 请将此路径替换为您实际的Excel文件路径
        String excelFilePath = "test.xlsx";

        try {
            // 获取工作表名称
            List<String> sheetNames = ExcelReader.getSheetNames(excelFilePath);
            System.out.println("工作表名称: " + sheetNames);

            // 获取工作表信息
            if (!sheetNames.isEmpty()) {
                Map<String, Integer> sheetInfo = ExcelReader.getSheetInfo(excelFilePath, 0);
                System.out.println("第一个工作表信息: " + sheetInfo);

                // 读取Excel数据（假设有表头）
                List<Map<String, Object>> excelData = ExcelReader.readExcel(excelFilePath, 0, true);
                System.out.println("读取到 " + excelData.size() + " 行数据");

                // 显示前几行数据
                for (int i = 0; i < Math.min(3, excelData.size()); i++) {
                    System.out.println("第" + (i + 1) + "行: " + excelData.get(i));
                }
            }
        } catch (Exception e) {
            System.out.println("读取Excel文件时出错（这是正常的，因为示例文件可能不存在）: " + e.getMessage());
        }
    }

    /**
     * 演示数据比对功能
     */
    private static void demonstrateDataComparison() {
        System.out.println("\n=== 数据比对示例 ===");

        // 模拟Excel数据
        List<Map<String, Object>> excelData = createSampleExcelData();

        // 模拟数据库数据
        List<Map<String, Object>> databaseData = createSampleDatabaseData();

        // 定义比对的关键字段
        List<String> keyFields = Arrays.asList("id", "name");

        // 执行比对
        ExcelDataComparator.ComparisonResult result =
            ExcelDataComparator.compareData(excelData, databaseData, keyFields);

        // 打印比对结果
        ExcelDataComparator.printComparisonResult(result);
    }

    /**
     * 创建示例Excel数据
     */
    private static List<Map<String, Object>> createSampleExcelData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", 1);
        row1.put("name", "张三");
        row1.put("age", 25);
        row1.put("department", "技术部");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", 2);
        row2.put("name", "李四");
        row2.put("age", 30);
        row2.put("department", "销售部");
        data.add(row2);

        Map<String, Object> row3 = new HashMap<>();
        row3.put("id", 3);
        row3.put("name", "王五");
        row3.put("age", 28);
        row3.put("department", "人事部");
        data.add(row3);

        Map<String, Object> row4 = new HashMap<>();
        row4.put("id", 4);
        row4.put("name", "赵六");
        row4.put("age", 35);
        row4.put("department", "财务部");
        data.add(row4);

        return data;
    }

    /**
     * 创建示例数据库数据
     */
    private static List<Map<String, Object>> createSampleDatabaseData() {
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", 1);
        row1.put("name", "张三");
        row1.put("age", 26);  // 年龄不同
        row1.put("department", "技术部");
        data.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", 2);
        row2.put("name", "李四");
        row2.put("age", 30);
        row2.put("department", "市场部");  // 部门不同
        data.add(row2);

        // 缺少id=3的记录（王五）

        Map<String, Object> row4 = new HashMap<>();
        row4.put("id", 5);
        row4.put("name", "孙七");
        row4.put("age", 32);
        row4.put("department", "技术部");
        data.add(row4);

        return data;
    }
}