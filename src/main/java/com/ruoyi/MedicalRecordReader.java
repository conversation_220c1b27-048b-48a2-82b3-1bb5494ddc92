package com.ruoyi;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.InputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 病历详情Excel文件读取器
 * 专门用于读取resources目录下的"病历详情-X朱珂.xls"文件
 */
public class MedicalRecordReader {

    public static void main(String[] args) {
        System.out.println("🏥 病历详情Excel文件读取演示");
        System.out.println("================================================================================");
        
        // 读取resources目录下的Excel文件
        readMedicalRecordFromResources();
    }

    /**
     * 从resources目录读取病历详情Excel文件
     */
    public static void readMedicalRecordFromResources() {
        String fileName = "/病历详情-X朱珂.xls";
        
        try {
            // 从resources目录加载文件
            InputStream inputStream = MedicalRecordReader.class.getResourceAsStream(fileName);
            
            if (inputStream == null) {
                System.err.println("❌ 无法找到文件: " + fileName);
                System.err.println("请确保文件位于 src/main/resources/ 目录下");
                return;
            }
            
            System.out.println("✅ 成功找到文件: " + fileName);
            
            // 创建工作簿对象（.xls格式使用HSSFWorkbook）
            Workbook workbook = new HSSFWorkbook(inputStream);
            System.out.println("✅ 成功创建工作簿对象");
            
            // 显示工作簿基本信息
            displayWorkbookInfo(workbook);
            
            // 读取所有工作表
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                displaySheetContent(sheet, i);
            }
            
            workbook.close();
            inputStream.close();
            System.out.println("\n✅ 文件读取完成，资源已关闭");
            
        } catch (IOException e) {
            System.err.println("❌ 读取Excel文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示工作簿基本信息
     */
    private static void displayWorkbookInfo(Workbook workbook) {
        System.out.println("\n📊 工作簿信息:");
        System.out.println("工作表数量: " + workbook.getNumberOfSheets());
        
        System.out.println("工作表列表:");
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            System.out.println("  " + (i + 1) + ". " + sheet.getSheetName() + 
                             " (行数: " + (sheet.getLastRowNum() + 1) + ")");
        }
    }

    /**
     * 显示工作表内容
     */
    private static void displaySheetContent(Sheet sheet, int sheetIndex) {
        System.out.println("\n================================================================================");
        System.out.println("📋 工作表 " + (sheetIndex + 1) + ": " + sheet.getSheetName());
        System.out.println("================================================================================");
        
        if (sheet.getPhysicalNumberOfRows() == 0) {
            System.out.println("⚠️  工作表为空");
            return;
        }

        // 获取数据范围
        int firstRowNum = sheet.getFirstRowNum();
        int lastRowNum = sheet.getLastRowNum();
        
        System.out.println("数据范围: 第" + (firstRowNum + 1) + "行 到 第" + (lastRowNum + 1) + "行");
        
        // 读取并显示数据
        for (int rowIndex = firstRowNum; rowIndex <= lastRowNum; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                System.out.println("第" + (rowIndex + 1) + "行: [空行]");
                continue;
            }
            
            displayRow(row, rowIndex);
        }
    }

    /**
     * 显示单行数据
     */
    private static void displayRow(Row row, int rowIndex) {
        StringBuilder rowContent = new StringBuilder();
        rowContent.append("第").append(rowIndex + 1).append("行: ");

        int cellCount = 0;
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            String cellValue = getCellValueAsString(cell);
            
            // 只显示非空的单元格
            if (!cellValue.trim().isEmpty() && !cellValue.equals("[空]")) {
                if (cellCount > 0) {
                    rowContent.append(" | ");
                }
                
                // 限制每个单元格显示的长度，避免输出过长
                if (cellValue.length() > 30) {
                    cellValue = cellValue.substring(0, 27) + "...";
                }
                
                rowContent.append(cellValue);
                cellCount++;
            }
        }
        
        // 只显示有内容的行
        if (cellCount > 0) {
            System.out.println(rowContent.toString());
        }
    }

    /**
     * 获取单元格的值并转换为字符串
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "[空]";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
                
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    // 如果是日期格式
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.format(date);
                } else {
                    // 如果是数字
                    double numericValue = cell.getNumericCellValue();
                    // 如果是整数，不显示小数点
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
                
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
                
            case FORMULA:
                // 尝试获取公式的计算结果
                try {
                    return getCellValueAsString(cell); // 递归调用获取计算结果
                } catch (Exception e) {
                    return "[公式: " + cell.getCellFormula() + "]";
                }
                
            case BLANK:
                return "";
                
            case ERROR:
                return "[错误:" + cell.getErrorCellValue() + "]";
                
            default:
                return cell.toString().trim();
        }
    }

    /**
     * 详细分析病历数据结构
     */
    public static void analyzeMedicalRecord() {
        String fileName = "/病历详情-X朱珂.xls";
        
        try (InputStream inputStream = MedicalRecordReader.class.getResourceAsStream(fileName)) {
            if (inputStream == null) {
                System.err.println("❌ 无法找到文件: " + fileName);
                return;
            }
            
            Workbook workbook = new HSSFWorkbook(inputStream);
            
            System.out.println("\n🔍 病历数据结构分析");
            System.out.println("================================================================================");
            
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                analyzeSheet(sheet, sheetIndex);
            }
            
            workbook.close();
            
        } catch (IOException e) {
            System.err.println("❌ 分析文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 分析工作表结构
     */
    private static void analyzeSheet(Sheet sheet, int sheetIndex) {
        System.out.println("\n📊 工作表 " + (sheetIndex + 1) + " 分析: " + sheet.getSheetName());
        System.out.println("--------------------------------------------------");
        
        if (sheet.getPhysicalNumberOfRows() == 0) {
            System.out.println("工作表为空");
            return;
        }
        
        // 分析数据分布
        int totalRows = sheet.getLastRowNum() + 1;
        int nonEmptyRows = 0;
        int maxColumns = 0;
        
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null && row.getPhysicalNumberOfCells() > 0) {
                nonEmptyRows++;
                maxColumns = Math.max(maxColumns, row.getLastCellNum());
            }
        }
        
        System.out.println("总行数: " + totalRows);
        System.out.println("非空行数: " + nonEmptyRows);
        System.out.println("最大列数: " + maxColumns);
        
        // 显示前几行作为样本
        System.out.println("\n📝 数据样本 (前5行):");
        int sampleCount = 0;
        for (int rowIndex = 0; rowIndex <= sheet.getLastRowNum() && sampleCount < 5; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null && row.getPhysicalNumberOfCells() > 0) {
                displayRow(row, rowIndex);
                sampleCount++;
            }
        }
    }
}
