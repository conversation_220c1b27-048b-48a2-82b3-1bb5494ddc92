package com.ruoyi;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.InputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 详细的病历数据分析器
 * 提供更清晰的数据展示和分析
 */
public class DetailedMedicalRecordAnalyzer {

    public static void main(String[] args) {
        System.out.println("🏥 病历详情数据详细分析");
        System.out.println("================================================================================");
        
        analyzeMedicalRecordDetailed();
    }

    /**
     * 详细分析病历数据
     */
    public static void analyzeMedicalRecordDetailed() {
        String fileName = "/病历详情-X朱珂.xls";
        
        try (InputStream inputStream = DetailedMedicalRecordAnalyzer.class.getResourceAsStream(fileName)) {
            if (inputStream == null) {
                System.err.println("❌ 无法找到文件: " + fileName);
                return;
            }
            
            Workbook workbook = new HSSFWorkbook(inputStream);
            System.out.println("✅ 成功加载病历文件");
            
            // 分析每个工作表
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                
                if ("病历详情".equals(sheet.getSheetName())) {
                    analyzePatientInfo(sheet);
                } else if ("病程信息".equals(sheet.getSheetName())) {
                    analyzeMedicalProgress(sheet);
                } else {
                    analyzeGenericSheet(sheet, i);
                }
            }
            
            workbook.close();
            
        } catch (IOException e) {
            System.err.println("❌ 分析文件时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 分析患者基本信息
     */
    private static void analyzePatientInfo(Sheet sheet) {
        System.out.println("\n👤 患者基本信息分析");
        System.out.println("================================================================================");
        
        if (sheet.getPhysicalNumberOfRows() < 2) {
            System.out.println("⚠️  数据不足，无法分析患者信息");
            return;
        }
        
        Row headerRow = sheet.getRow(0);
        Row dataRow = sheet.getRow(1);
        
        if (headerRow == null || dataRow == null) {
            System.out.println("⚠️  表头或数据行为空");
            return;
        }
        
        // 创建字段映射并显示关键信息
        System.out.println("📋 关键信息提取:");
        System.out.println("--------------------------------------------------------------------------------");
        
        for (int i = 0; i < headerRow.getLastCellNum() && i < dataRow.getLastCellNum(); i++) {
            String fieldName = getCellValueAsString(headerRow.getCell(i));
            String fieldValue = getCellValueAsString(dataRow.getCell(i));
            
            if (!fieldName.trim().isEmpty() && !fieldValue.trim().isEmpty()) {
                // 格式化显示重要字段
                if (isImportantField(fieldName)) {
                    System.out.printf("%-12s: %s%n", fieldName, fieldValue);
                }
            }
        }
        
        System.out.println("\n📊 完整字段列表:");
        System.out.println("--------------------------------------------------------------------------------");
        
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            String fieldName = getCellValueAsString(headerRow.getCell(i));
            String fieldValue = "";
            
            if (i < dataRow.getLastCellNum()) {
                fieldValue = getCellValueAsString(dataRow.getCell(i));
            }
            
            if (!fieldName.trim().isEmpty()) {
                // 限制显示长度
                if (fieldValue.length() > 50) {
                    fieldValue = fieldValue.substring(0, 47) + "...";
                }
                System.out.printf("%-15s: %s%n", fieldName, fieldValue);
            }
        }
    }

    /**
     * 分析病程信息
     */
    private static void analyzeMedicalProgress(Sheet sheet) {
        System.out.println("\n📝 病程信息分析");
        System.out.println("================================================================================");
        
        if (sheet.getPhysicalNumberOfRows() < 2) {
            System.out.println("⚠️  无病程记录");
            return;
        }
        
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            System.out.println("⚠️  病程信息表头缺失");
            return;
        }
        
        // 显示表头
        System.out.println("📋 病程记录字段:");
        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            String fieldName = getCellValueAsString(headerRow.getCell(i));
            if (!fieldName.trim().isEmpty()) {
                System.out.print(fieldName + " | ");
            }
        }
        System.out.println();
        System.out.println("--------------------------------------------------------------------------------");
        
        // 显示每条病程记录
        int recordCount = 0;
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            
            boolean hasData = false;
            StringBuilder record = new StringBuilder();
            
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                String fieldName = getCellValueAsString(headerRow.getCell(i));
                String fieldValue = "";
                
                if (i < row.getLastCellNum()) {
                    fieldValue = getCellValueAsString(row.getCell(i));
                }
                
                if (!fieldValue.trim().isEmpty()) {
                    hasData = true;
                    if (record.length() > 0) {
                        record.append(" | ");
                    }
                    record.append(fieldName).append(": ").append(fieldValue);
                }
            }
            
            if (hasData) {
                recordCount++;
                System.out.println("记录 " + recordCount + ": " + record.toString());
            }
        }
        
        System.out.println("\n📊 病程统计: 共 " + recordCount + " 条记录");
    }

    /**
     * 分析通用工作表
     */
    private static void analyzeGenericSheet(Sheet sheet, int sheetIndex) {
        System.out.println("\n📄 工作表分析: " + sheet.getSheetName());
        System.out.println("================================================================================");
        
        if (sheet.getPhysicalNumberOfRows() == 0) {
            System.out.println("⚠️  工作表为空");
            return;
        }
        
        System.out.println("总行数: " + (sheet.getLastRowNum() + 1));
        
        // 显示前几行数据
        int displayRows = Math.min(5, sheet.getLastRowNum() + 1);
        for (int rowIndex = 0; rowIndex < displayRows; rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                System.out.print("第" + (rowIndex + 1) + "行: ");
                for (int i = 0; i < row.getLastCellNum(); i++) {
                    String cellValue = getCellValueAsString(row.getCell(i));
                    if (!cellValue.trim().isEmpty()) {
                        System.out.print(cellValue + " | ");
                    }
                }
                System.out.println();
            }
        }
    }

    /**
     * 判断是否为重要字段
     */
    private static boolean isImportantField(String fieldName) {
        String[] importantFields = {
            "患者姓名", "病历编号", "性别", "年龄", "身份证号", 
            "诊断", "科室", "就诊日期", "手机号", "联系人"
        };
        
        for (String field : importantFields) {
            if (field.equals(fieldName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取单元格的值并转换为字符串
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
                
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    return sdf.format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
                
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
                
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return "[公式]";
                }
                
            case BLANK:
                return "";
                
            case ERROR:
                return "[错误]";
                
            default:
                return cell.toString().trim();
        }
    }
}
